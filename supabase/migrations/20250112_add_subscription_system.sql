-- Add subscription-related fields to projects table
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS project_type TEXT DEFAULT 'one_time' CHECK (project_type IN ('one_time', 'subscription')),
ADD COLUMN IF NOT EXISTS billing_cycle TEXT CHECK (billing_cycle IN ('monthly', 'quarterly', 'yearly')),
ADD COLUMN IF NOT EXISTS billing_amount DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS billing_start_date DATE,
ADD COLUMN IF NOT EXISTS billing_end_date DATE,
ADD COLUMN IF NOT EXISTS next_billing_date DATE,
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'paused', 'cancelled', 'expired')),
ADD COLUMN IF NOT EXISTS auto_generate_invoices BOOLEAN DEFAULT true;

-- Create subscription_invoices table for tracking recurring invoices
CREATE TABLE IF NOT EXISTS subscription_invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  invoice_id UUID REFERENCES invoices(id) ON DELETE SET NULL,
  billing_period_start DATE NOT NULL,
  billing_period_end DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'IDR',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'generated', 'sent', 'paid', 'failed')),
  due_date DATE NOT NULL,
  generated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscription_invoices_project_id ON subscription_invoices(project_id);
CREATE INDEX IF NOT EXISTS idx_subscription_invoices_status ON subscription_invoices(status);
CREATE INDEX IF NOT EXISTS idx_subscription_invoices_due_date ON subscription_invoices(due_date);
CREATE INDEX IF NOT EXISTS idx_projects_subscription_status ON projects(subscription_status) WHERE project_type = 'subscription';
CREATE INDEX IF NOT EXISTS idx_projects_next_billing_date ON projects(next_billing_date) WHERE project_type = 'subscription';

-- Create function to calculate next billing date
CREATE OR REPLACE FUNCTION calculate_next_billing_date(
  current_date DATE,
  cycle TEXT
) RETURNS DATE AS $$
BEGIN
  CASE cycle
    WHEN 'monthly' THEN
      RETURN current_date + INTERVAL '1 month';
    WHEN 'quarterly' THEN
      RETURN current_date + INTERVAL '3 months';
    WHEN 'yearly' THEN
      RETURN current_date + INTERVAL '1 year';
    ELSE
      RETURN current_date + INTERVAL '1 month';
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate subscription invoices
CREATE OR REPLACE FUNCTION generate_subscription_invoices()
RETURNS TABLE(project_id UUID, invoice_count INTEGER) AS $$
DECLARE
  project_record RECORD;
  new_invoice_id UUID;
  billing_period_start DATE;
  billing_period_end DATE;
  invoice_count INTEGER := 0;
BEGIN
  -- Process all active subscription projects that are due for billing
  FOR project_record IN
    SELECT p.id, p.name, p.client_id, p.billing_cycle, p.billing_amount, 
           p.next_billing_date, p.currency, p.description
    FROM projects p
    WHERE p.project_type = 'subscription'
      AND p.subscription_status = 'active'
      AND p.auto_generate_invoices = true
      AND p.next_billing_date <= CURRENT_DATE
      AND (p.billing_end_date IS NULL OR p.next_billing_date <= p.billing_end_date)
  LOOP
    -- Calculate billing period
    billing_period_start := project_record.next_billing_date;
    billing_period_end := calculate_next_billing_date(billing_period_start, project_record.billing_cycle) - INTERVAL '1 day';
    
    -- Check if subscription invoice already exists for this period
    IF NOT EXISTS (
      SELECT 1 FROM subscription_invoices si
      WHERE si.project_id = project_record.id
        AND si.billing_period_start = billing_period_start
        AND si.billing_period_end = billing_period_end
    ) THEN
      -- Create the actual invoice
      INSERT INTO invoices (
        client_id,
        project_id,
        amount,
        tax_amount,
        discount_amount,
        total_amount,
        currency,
        status,
        invoice_date,
        due_date,
        items,
        notes,
        milestone_type
      ) VALUES (
        project_record.client_id,
        project_record.id,
        project_record.billing_amount,
        0, -- No tax for subscription invoices by default
        0, -- No discount by default
        project_record.billing_amount,
        COALESCE(project_record.currency, 'IDR'),
        'draft',
        CURRENT_DATE,
        CURRENT_DATE + INTERVAL '30 days', -- 30 days payment terms
        jsonb_build_array(
          jsonb_build_object(
            'id', gen_random_uuid()::text,
            'description', CASE 
              WHEN project_record.billing_cycle = 'monthly' THEN 'Monthly subscription for ' || project_record.name
              WHEN project_record.billing_cycle = 'quarterly' THEN 'Quarterly subscription for ' || project_record.name
              WHEN project_record.billing_cycle = 'yearly' THEN 'Yearly subscription for ' || project_record.name
              ELSE 'Subscription for ' || project_record.name
            END,
            'quantity', 1,
            'rate', project_record.billing_amount,
            'amount', project_record.billing_amount
          )
        ),
        'Auto-generated subscription invoice for billing period: ' || billing_period_start || ' to ' || billing_period_end,
        'standard'
      ) RETURNING id INTO new_invoice_id;
      
      -- Create subscription invoice record
      INSERT INTO subscription_invoices (
        project_id,
        invoice_id,
        billing_period_start,
        billing_period_end,
        amount,
        currency,
        status,
        due_date,
        generated_at
      ) VALUES (
        project_record.id,
        new_invoice_id,
        billing_period_start,
        billing_period_end,
        project_record.billing_amount,
        COALESCE(project_record.currency, 'IDR'),
        'generated',
        CURRENT_DATE + INTERVAL '30 days',
        NOW()
      );
      
      -- Update project's next billing date
      UPDATE projects 
      SET next_billing_date = calculate_next_billing_date(project_record.next_billing_date, project_record.billing_cycle),
          updated_at = NOW()
      WHERE id = project_record.id;
      
      invoice_count := invoice_count + 1;
      
      -- Return the project_id and count for this iteration
      project_id := project_record.id;
      RETURN NEXT;
    END IF;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_subscription_invoices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_subscription_invoices_updated_at
  BEFORE UPDATE ON subscription_invoices
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_invoices_updated_at();

-- Schedule the subscription invoice generation to run daily at 9 AM
SELECT cron.schedule(
  'generate-subscription-invoices',
  '0 9 * * *', -- Daily at 9 AM
  $$
  SELECT generate_subscription_invoices();
  $$
);

-- Add RLS policies for subscription_invoices
ALTER TABLE subscription_invoices ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view subscription invoices for projects they have access to
CREATE POLICY "Users can view subscription invoices for accessible projects" ON subscription_invoices
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = subscription_invoices.project_id
      AND (
        p.created_by = auth.uid()
        OR EXISTS (
          SELECT 1 FROM users_profiles up
          WHERE up.id = auth.uid()
          AND up.role IN ('admin', 'manager')
        )
      )
    )
  );

-- Policy: Only admins and managers can insert subscription invoices
CREATE POLICY "Only admins and managers can insert subscription invoices" ON subscription_invoices
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users_profiles up
      WHERE up.id = auth.uid()
      AND up.role IN ('admin', 'manager')
    )
  );

-- Policy: Only admins and managers can update subscription invoices
CREATE POLICY "Only admins and managers can update subscription invoices" ON subscription_invoices
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users_profiles up
      WHERE up.id = auth.uid()
      AND up.role IN ('admin', 'manager')
    )
  );

-- Policy: Only admins can delete subscription invoices
CREATE POLICY "Only admins can delete subscription invoices" ON subscription_invoices
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users_profiles up
      WHERE up.id = auth.uid()
      AND up.role = 'admin'
    )
  );

-- Add comment for documentation
COMMENT ON TABLE subscription_invoices IS 'Tracks recurring invoices for subscription-based projects';
COMMENT ON FUNCTION generate_subscription_invoices() IS 'Automatically generates invoices for active subscription projects that are due for billing';
COMMENT ON FUNCTION calculate_next_billing_date(DATE, TEXT) IS 'Calculates the next billing date based on current date and billing cycle';
