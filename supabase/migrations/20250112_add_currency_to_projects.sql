-- Add currency column to projects table
-- This fixes the missing currency column that was referenced in the subscription system
-- but never actually added to the projects table schema

ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'IDR' CHECK (currency IN ('USD', 'IDR'));

-- Add comment for documentation
COMMENT ON COLUMN projects.currency IS 'Currency for project billing and invoicing. Defaults to IDR for Indonesian business context.';

-- Update existing projects to have IDR currency if they don't have one set
UPDATE projects 
SET currency = 'IDR' 
WHERE currency IS NULL;
