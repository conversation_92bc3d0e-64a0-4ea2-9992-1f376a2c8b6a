'use client'

import { useState } from "react"
import { SubscriptionList } from "@/components/subscriptions/subscription-list"
import { SubscriptionAnalyticsCard } from "@/components/subscriptions/subscription-analytics-card"
import { ManualInvoiceGenerator } from "@/components/subscriptions/manual-invoice-generator"

export default function SubscriptionsPage() {
  const [refreshKey, setRefreshKey] = useState(0)

  const handleProjectSelect = () => {
    // Future implementation for project details view
  }

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Analytics and Tools */}
      <div className="grid gap-6 md:grid-cols-2">
        <SubscriptionAnalyticsCard key={refreshKey} />
        <ManualInvoiceGenerator onSuccess={handleRefresh} />
      </div>

      {/* Subscription List */}
      <SubscriptionList
        key={refreshKey}
        onProjectSelect={handleProjectSelect}
      />
    </div>
  )
}
