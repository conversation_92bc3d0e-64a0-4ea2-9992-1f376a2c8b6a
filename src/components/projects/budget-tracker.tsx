"use client"

import React from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Banknote, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react"
import { formatCurrency, type Currency } from "@/lib/utils/currency"
import { getBudgetWarningLevel, getBudgetWarningMessage } from "@/lib/utils/budget-validation"

interface BudgetTrackerProps {
  projectBudget: number
  totalInvoiced: number
  currency?: Currency
  className?: string
  showDetails?: boolean
}

export function BudgetTracker({
  projectBudget,
  totalInvoiced,
  currency = "IDR",
  className = "",
  showDetails = true
}: BudgetTrackerProps) {
  const remainingBudget = projectBudget - totalInvoiced
  const utilizationPercentage = projectBudget > 0 ? Math.round((totalInvoiced / projectBudget) * 100) : 0
  const warningLevel = getBudgetWarningLevel(utilizationPercentage)
  

  
  const getStatusIcon = () => {
    switch (warningLevel) {
      case 'danger': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning': return <TrendingUp className="h-4 w-4 text-yellow-500" />
      default: return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }
  
  const getStatusBadge = () => {
    switch (warningLevel) {
      case 'danger': return <Badge variant="destructive">Over Budget Risk</Badge>
      case 'warning': return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Budget Warning</Badge>
      default: return <Badge variant="secondary" className="bg-green-100 text-green-800">On Track</Badge>
    }
  }

  if (!showDetails) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {getStatusIcon()}
        <span className="text-sm font-medium">{utilizationPercentage}% utilized</span>
        <Progress 
          value={Math.min(utilizationPercentage, 100)} 
          className="w-20 h-2"
        />
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Banknote className="h-5 w-5" />
            Budget Tracking
          </CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Budget Overview */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-sm text-muted-foreground">Project Budget</p>
            <p className="text-lg font-bold">{formatCurrency(projectBudget, currency)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Total Invoiced</p>
            <p className="text-lg font-bold text-blue-600">{formatCurrency(totalInvoiced, currency)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Remaining</p>
            <p className={`text-lg font-bold ${remainingBudget < 0 ? 'text-red-600' : 'text-green-600'}`}>
              {formatCurrency(remainingBudget, currency)}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Budget Utilization</span>
            <span className="font-medium">{utilizationPercentage}%</span>
          </div>
          <Progress 
            value={Math.min(utilizationPercentage, 100)} 
            className="h-3"
          />
          {utilizationPercentage > 100 && (
            <p className="text-xs text-red-600">
              Exceeded by {formatCurrency(Math.abs(remainingBudget), currency)}
            </p>
          )}
        </div>

        {/* Warning/Status Message */}
        {warningLevel !== 'safe' && (
          <Alert className={warningLevel === 'danger' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
            {getStatusIcon()}
            <AlertDescription className="ml-2">
              {getBudgetWarningMessage(utilizationPercentage, remainingBudget, currency)}
            </AlertDescription>
          </Alert>
        )}

        {/* Budget Breakdown */}
        <div className="pt-2 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Utilization Rate:</span>
              <span className="font-medium">{utilizationPercentage}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Available:</span>
              <span className={`font-medium ${remainingBudget < 0 ? 'text-red-600' : 'text-green-600'}`}>
                {remainingBudget < 0 ? 'Over Budget' : formatCurrency(remainingBudget, currency)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface CompactBudgetTrackerProps {
  projectBudget: number
  totalInvoiced: number
  currency?: Currency
  className?: string
}

export function CompactBudgetTracker({
  projectBudget,
  totalInvoiced,
  currency = "IDR" as Currency,
  className = ""
}: CompactBudgetTrackerProps) {
  const utilizationPercentage = projectBudget > 0 ? Math.round((totalInvoiced / projectBudget) * 100) : 0
  const warningLevel = getBudgetWarningLevel(utilizationPercentage)
  
  const getTextColor = () => {
    switch (warningLevel) {
      case 'danger': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      default: return 'text-green-600'
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex-1">
        <div className="flex items-center justify-between text-xs mb-1">
          <span className="text-muted-foreground">Budget</span>
          <span className={`font-medium ${getTextColor()}`}>
            {utilizationPercentage}%
          </span>
        </div>
        <Progress 
          value={Math.min(utilizationPercentage, 100)} 
          className="h-1"
        />
      </div>
      <div className="text-right">
        <div className="text-xs text-muted-foreground">
          {formatCurrency(totalInvoiced, currency)}
        </div>
        <div className="text-xs font-medium">
          of {formatCurrency(projectBudget, currency)}
        </div>
      </div>
    </div>
  )
}
