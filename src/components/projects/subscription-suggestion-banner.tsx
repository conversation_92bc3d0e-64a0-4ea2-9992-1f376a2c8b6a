'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ProjectWithSubscription } from "@/lib/types"
import { Repeat, X, ArrowRight, Calendar, Banknote } from "lucide-react"
import { SubscriptionProjectForm } from "@/components/subscriptions/subscription-project-form"

interface SubscriptionSuggestionBannerProps {
  project: ProjectWithSubscription
  onConvert?: () => void
}

const recurringServiceTypes = [
  'jasa_maintenance_website',
  'wordpress_hosting'
]

const serviceTypeLabels: Record<string, string> = {
  'jasa_maintenance_website': 'Website Maintenance',
  'wordpress_hosting': 'WordPress Hosting'
}

const suggestedBillingCycles: Record<string, { cycle: 'monthly' | 'quarterly' | 'yearly', amount: number }> = {
  'jasa_maintenance_website': { cycle: 'monthly', amount: 500000 }, // 500k IDR per month
  'wordpress_hosting': { cycle: 'monthly', amount: 150000 }, // 150k IDR per month
}

export function SubscriptionSuggestionBanner({ project, onConvert }: SubscriptionSuggestionBannerProps) {
  const [dismissed, setDismissed] = useState(false)
  const [showConvertForm, setShowConvertForm] = useState(false)

  // Only show for one-time projects with recurring service types
  if (
    dismissed || 
    project.project_type === 'subscription' || 
    !project.service_type || 
    !recurringServiceTypes.includes(project.service_type)
  ) {
    return null
  }

  const serviceLabel = serviceTypeLabels[project.service_type] || project.service_type
  const suggestion = suggestedBillingCycles[project.service_type]

  const handleConvert = () => {
    setShowConvertForm(true)
  }

  const handleConvertSuccess = () => {
    setShowConvertForm(false)
    onConvert?.()
  }

  const handleConvertCancel = () => {
    setShowConvertForm(false)
  }

  if (showConvertForm) {
    return (
      <SubscriptionProjectForm
        project={{
          ...project,
          project_type: 'subscription' as const,
          billing_cycle: suggestion?.cycle || 'monthly',
          billing_amount: suggestion?.amount || 0,
          billing_start_date: new Date().toISOString().split('T')[0],
          subscription_status: 'active' as const,
          auto_generate_invoices: true,
          status: 'in_progress' as const
        }}
        onSuccess={handleConvertSuccess}
        onCancel={handleConvertCancel}
      />
    )
  }

  return (
    <Alert className="border-blue-200 bg-blue-50">
      <div className="flex items-start justify-between w-full">
        <div className="flex items-start space-x-3 flex-1">
          <Repeat className="h-5 w-5 text-blue-600 mt-0.5" />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h4 className="font-medium text-blue-900">Convert to Subscription Project</h4>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {serviceLabel}
              </Badge>
            </div>
            <AlertDescription className="text-blue-800 mb-3">
              This project appears to be a recurring service. Converting it to a subscription project will enable automatic invoice generation and better billing management.
            </AlertDescription>
            
            {suggestion && (
              <div className="flex items-center gap-4 text-sm text-blue-700 mb-3">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Suggested: {suggestion.cycle} billing</span>
                </div>
                <div className="flex items-center gap-1">
                  <Banknote className="h-4 w-4" />
                  <span>
                    {new Intl.NumberFormat('id-ID', {
                      style: 'currency',
                      currency: 'IDR',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(suggestion.amount).replace(/IDR\s?/, 'Rp ')}
                  </span>
                </div>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button 
                size="sm" 
                onClick={handleConvert}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Convert to Subscription
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setDismissed(true)}
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                Maybe Later
              </Button>
            </div>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setDismissed(true)}
          className="text-blue-600 hover:text-blue-800 hover:bg-blue-100 p-1 h-auto"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  )
}
