"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSkeleton } from "@/components/ui/loading-skeleton"
import { Pagination } from "@/components/ui/pagination"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { getProjects, searchProjects, getProjectsByStatus, deleteProject } from "@/lib/api/projects-client"
import { ProjectWithRelations, ProjectStatus } from "@/lib/types"
import { usePagination } from "@/hooks/usePagination"
import { Search, Plus, <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Eye, Calendar, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ProjectForm } from "./project-form"
import { formatDistanceToNow } from "date-fns"

// Service type formatting function
const formatServiceType = (serviceType: string | null) => {
  if (!serviceType) return '-'

  const serviceTypeMap: Record<string, string> = {
    'jasa_pembuatan_website': 'Pembuatan Website',
    'jasa_redesign_website': 'Redesign Website',
    'wordpress_hosting': 'WordPress Hosting',
    'jasa_maintenance_website': 'Maintenance Website',
    'jasa_perbaikan_website': 'Perbaikan Website',
    'jasa_remove_malware': 'Remove Malware',
    'jasa_migrasi_hosting': 'Migrasi Hosting',
    'jasa_migrasi_website_ke_astro': 'Migrasi ke Astro',
    'jasa_konversi_wordpress_ke_blocks': 'Konversi ke Blocks',
    'jasa_audit_optimasi_seo_onpage': 'Audit & SEO On-page'
  }

  return serviceTypeMap[serviceType] || serviceType
}

interface ProjectListProps {
  onProjectSelect?: (project: ProjectWithRelations) => void
}

export function ProjectList({}: ProjectListProps) {
  const [projects, setProjects] = useState<ProjectWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [searchInput, setSearchInput] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | "all">("all")
  const [showForm, setShowForm] = useState(false)
  const [editingProject, setEditingProject] = useState<ProjectWithRelations | undefined>()

  // Debounce search input
  useEffect(() => {
    setIsSearching(true)
    const timeoutId = setTimeout(() => {
      // Only search if input has 3+ characters or is empty
      if (searchInput.length === 0 || searchInput.length >= 3) {
        setDebouncedSearchQuery(searchInput)
      }
      setIsSearching(false)
    }, 500) // 500ms debounce delay

    return () => {
      clearTimeout(timeoutId)
    }
  }, [searchInput])

  const fetchProjects = useCallback(async () => {
    setLoading(true)
    try {
      let data
      if (debouncedSearchQuery) {
        data = await searchProjects(debouncedSearchQuery)
      } else if (statusFilter !== "all") {
        data = await getProjectsByStatus(statusFilter)
      } else {
        data = await getProjects()
      }
      setProjects(data)
    } catch (error) {
      console.error("Failed to fetch projects:", error)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery, statusFilter])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  const handleDeleteProject = async (id: string) => {
    if (confirm("Are you sure you want to delete this project?")) {
      try {
        await deleteProject(id)
        fetchProjects()
      } catch (error) {
        console.error("Failed to delete project:", error)
      }
    }
  }

  const handleEditProject = (project: ProjectWithRelations) => {
    setEditingProject(project)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    fetchProjects()
    setEditingProject(undefined)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace(/IDR\s?/, 'Rp ')
  }

  // Memoized filtered projects to prevent unnecessary recalculations
  const filteredProjects = useMemo(() => {
    if (!projects.length) return [];

    return projects.filter(project => {
      const matchesSearch = !debouncedSearchQuery ||
        project.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        project.description?.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        project.client?.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      const matchesStatus = statusFilter === 'all' || project.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [projects, debouncedSearchQuery, statusFilter]);

  // Pagination hook
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedProjects,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination,
  } = usePagination({ data: filteredProjects, initialItemsPerPage: 20 });

  // Reset pagination when filters change
  useEffect(() => {
    resetPagination();
  }, [debouncedSearchQuery, statusFilter, resetPagination]);

  if (loading) {
    return <LoadingSkeleton type="list" count={3} />
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          {isSearching && searchInput.length >= 3 && (
            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 animate-spin" />
          )}
          <Input
            placeholder="Search projects by name, description, or client (min 3 characters)..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10 pr-10 h-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={(value: ProjectStatus | "all") => setStatusFilter(value)}>
          <SelectTrigger className="w-full sm:w-[180px] h-10">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="planning">Planning</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="review">Review</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowForm(true)} className="h-10 px-6">
          <Plus className="mr-2 h-4 w-4" />
          Add Project
        </Button>
      </div>

      {/* Project List */}
      {loading ? (
        <LoadingSkeleton type="table" />
      ) : filteredProjects.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {debouncedSearchQuery || statusFilter !== "all"
                ? "No projects found matching your criteria."
                : "No projects yet. Create your first project to get started."
              }
            </p>
            {searchInput.length > 0 && searchInput.length < 3 && (
              <p className="text-sm text-muted-foreground mt-2">
                Type at least 3 characters to search
              </p>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card className="border-border/50 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/40">
                  <TableHead>Name</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead className="hidden sm:table-cell">Status</TableHead>
                  <TableHead className="hidden md:table-cell">Service Type</TableHead>
                  <TableHead className="hidden lg:table-cell text-right">Budget</TableHead>
                  <TableHead className="hidden xl:table-cell">Due Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedProjects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <Link
                          href={`/projects/${project.id}`}
                          className="font-medium hover:text-primary transition-colors cursor-pointer"
                        >
                          {project.name}
                        </Link>
                        {project.description && (
                          <span className="text-sm text-muted-foreground line-clamp-1">
                            {project.description}
                          </span>
                        )}
                        {project.assigned_team_members && project.assigned_team_members.length > 0 && (
                          <div className="flex gap-1 flex-wrap mt-1">
                            {project.assigned_team_members.slice(0, 2).map((member) => (
                              <Badge key={member.id} variant="secondary" className="text-xs">
                                {member.full_name}
                              </Badge>
                            ))}
                            {project.assigned_team_members.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{project.assigned_team_members.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {project.client ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{project.client.name}</span>
                          {project.client.company && (
                            <span className="text-sm text-muted-foreground">{project.client.company}</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No client</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <StatusBadge status={project.status} />
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <span className="text-sm">{formatServiceType(project.service_type)}</span>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell text-right">
                      {project.budget ? (
                        <span className="font-medium">{formatCurrency(project.budget)}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No budget</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      {project.end_date ? (
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          {formatDistanceToNow(new Date(project.end_date), { addSuffix: true })}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No due date</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">More actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/projects/${project.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditProject(project)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteProject(project.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* Project Form Dialog */}
      <ProjectForm
        open={showForm}
        onOpenChange={setShowForm}
        project={editingProject}
        onSuccess={handleFormSuccess}
      />
    </div>
  )
}
