'use client'

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { subscriptionProjectFormSchema, type SubscriptionProjectFormData } from "@/lib/validations"
import { createSubscriptionProject, updateSubscriptionProject } from "@/lib/api/subscriptions-client"
import { getClients } from "@/lib/api/clients-client"
import { Client, ProjectWithSubscription, BillingCycle, SubscriptionStatus } from "@/lib/types"
import { Loader2, DollarSign, Repeat, Setting<PERSON> } from "lucide-react"

interface SubscriptionProjectFormProps {
  project?: ProjectWithSubscription
  onSuccess?: (project: ProjectWithSubscription) => void
  onCancel?: () => void
}

const serviceTypeOptions = [
  { value: "jasa_pembuatan_website", label: "Jasa pembuatan website" },
  { value: "jasa_redesign_website", label: "Jasa redesign website" },
  { value: "wordpress_hosting", label: "WordPress hosting" },
  { value: "jasa_maintenance_website", label: "Jasa maintenance website" },
  { value: "jasa_perbaikan_website", label: "Jasa perbaikan website" },
  { value: "jasa_remove_malware", label: "Jasa remove malware" },
  { value: "jasa_migrasi_hosting", label: "Jasa migrasi hosting" },
  { value: "jasa_migrasi_website_ke_astro", label: "Jasa migrasi website ke Astro" },
  { value: "jasa_konversi_wordpress_ke_blocks", label: "Jasa konversi WordPress ke Blocks" },
  { value: "jasa_audit_optimasi_seo_onpage", label: "Jasa audit & optimasi SEO On-page" },
]

const billingCycleOptions = [
  { value: "monthly", label: "Monthly" },
  { value: "quarterly", label: "Quarterly" },
  { value: "yearly", label: "Yearly" },
]

export function SubscriptionProjectForm({ project, onSuccess, onCancel }: SubscriptionProjectFormProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingClients, setLoadingClients] = useState(true)

  const isEditing = !!project

  const form = useForm({
    resolver: zodResolver(subscriptionProjectFormSchema),
    defaultValues: {
      name: project?.name || "",
      description: project?.description || "",
      client_id: project?.client_id || "",
      project_type: "subscription",
      billing_cycle: (project?.billing_cycle as BillingCycle) || "monthly",
      billing_amount: project?.billing_amount || 0,
      billing_start_date: project?.billing_start_date || "",
      billing_end_date: project?.billing_end_date || "",
      currency: "IDR",
      subscription_status: (project?.subscription_status as SubscriptionStatus) || "active",
      auto_generate_invoices: project?.auto_generate_invoices ?? true,
      status: project?.status || "in_progress",
      service_type: project?.service_type || undefined,
    },
  })

  useEffect(() => {
    async function fetchClients() {
      try {
        const clientsData = await getClients()
        setClients(clientsData)
      } catch {
        setError("Failed to load clients")
      } finally {
        setLoadingClients(false)
      }
    }

    fetchClients()
  }, [])

  const onSubmit = async (data: SubscriptionProjectFormData) => {
    setLoading(true)
    setError(null)

    try {
      let result: ProjectWithSubscription

      if (isEditing && project) {
        result = await updateSubscriptionProject(project.id, data)
      } else {
        result = await createSubscriptionProject(data)
      }

      onSuccess?.(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  if (loadingClients) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Repeat className="h-5 w-5" />
          {isEditing ? "Edit Subscription Project" : "Create Subscription Project"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Project Name *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter project name"
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="client_id">Client *</Label>
                <Select
                  value={form.watch("client_id")}
                  onValueChange={(value) => form.setValue("client_id", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a client" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.name} {client.company && `(${client.company})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.client_id && (
                  <p className="text-sm text-destructive">{form.formState.errors.client_id.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Enter project description"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="service_type">Service Type</Label>
              <Select
                value={form.watch("service_type") || ""}
                onValueChange={(value) => form.setValue("service_type", value === "" ? undefined : value as "jasa_pembuatan_website" | "jasa_redesign_website" | "wordpress_hosting" | "jasa_maintenance_website" | "jasa_perbaikan_website" | "jasa_remove_malware" | "jasa_migrasi_hosting" | "jasa_migrasi_website_ke_astro" | "jasa_konversi_wordpress_ke_blocks" | "jasa_audit_optimasi_seo_onpage")}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select service type" />
                </SelectTrigger>
                <SelectContent>
                  {serviceTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Billing Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Billing Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="billing_cycle">Billing Cycle *</Label>
                <Select
                  value={form.watch("billing_cycle")}
                  onValueChange={(value) => form.setValue("billing_cycle", value as BillingCycle)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select billing cycle" />
                  </SelectTrigger>
                  <SelectContent>
                    {billingCycleOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.billing_cycle && (
                  <p className="text-sm text-destructive">{form.formState.errors.billing_cycle.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="billing_amount">Billing Amount (IDR) *</Label>
                <Input
                  id="billing_amount"
                  type="number"
                  step="0.01"
                  {...form.register("billing_amount", { valueAsNumber: true })}
                  placeholder="0.00"
                />
                {form.formState.errors.billing_amount && (
                  <p className="text-sm text-destructive">{form.formState.errors.billing_amount.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={form.watch("currency")}
                  onValueChange={(value) => form.setValue("currency", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IDR">IDR (Indonesian Rupiah)</SelectItem>
                    <SelectItem value="USD">USD (US Dollar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="billing_start_date">Billing Start Date *</Label>
                <Input
                  id="billing_start_date"
                  type="date"
                  {...form.register("billing_start_date")}
                />
                {form.formState.errors.billing_start_date && (
                  <p className="text-sm text-destructive">{form.formState.errors.billing_start_date.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="billing_end_date">Billing End Date (Optional)</Label>
                <Input
                  id="billing_end_date"
                  type="date"
                  {...form.register("billing_end_date")}
                />
              </div>
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Settings
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="subscription_status">Subscription Status</Label>
                <Select
                  value={form.watch("subscription_status")}
                  onValueChange={(value) => form.setValue("subscription_status", value as SubscriptionStatus)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="paused">Paused</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Project Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) => form.setValue("status", value as "planning" | "in_progress" | "review" | "completed" | "cancelled")}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="review">Review</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="auto_generate_invoices"
                checked={form.watch("auto_generate_invoices")}
                onCheckedChange={(checked) => form.setValue("auto_generate_invoices", checked)}
              />
              <Label htmlFor="auto_generate_invoices">
                Auto-generate invoices based on billing cycle
              </Label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update Subscription" : "Create Subscription"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
