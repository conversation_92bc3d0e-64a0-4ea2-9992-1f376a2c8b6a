'use client'

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { generateSubscriptionInvoices } from "@/lib/api/subscriptions-client"
import { Loader2, Zap, CheckCircle, AlertCircle, Calendar } from "lucide-react"

interface ManualInvoiceGeneratorProps {
  onSuccess?: () => void
}

export function ManualInvoiceGenerator({ onSuccess }: ManualInvoiceGeneratorProps) {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string; count: number } | null>(null)

  const handleGenerateInvoices = async () => {
    setLoading(true)
    setResult(null)

    try {
      const response = await generateSubscriptionInvoices()
      setResult(response)
      
      if (response.success && response.count > 0) {
        onSuccess?.()
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to generate invoices',
        count: 0
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Manual Invoice Generation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Generate invoices for all active subscription projects that are due for billing. 
          This process runs automatically daily, but you can trigger it manually here.
        </div>

        {result && (
          <Alert variant={result.success ? "default" : "destructive"}>
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {result.message}
                {result.success && result.count > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {result.count} invoice{result.count !== 1 ? 's' : ''} generated
                  </Badge>
                )}
              </AlertDescription>
            </div>
          </Alert>
        )}

        <div className="flex items-center gap-2">
          <Button 
            onClick={handleGenerateInvoices} 
            disabled={loading}
            className="flex-1"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Calendar className="mr-2 h-4 w-4" />
            Generate Due Invoices
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <strong>Note:</strong> Only subscription projects with:
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Active subscription status</li>
            <li>Auto-generate invoices enabled</li>
            <li>Next billing date is today or past due</li>
            <li>No existing invoice for the current billing period</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
