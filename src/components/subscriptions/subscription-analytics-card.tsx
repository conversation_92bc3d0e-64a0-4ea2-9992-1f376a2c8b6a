'use client'

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getSubscriptionAnalytics } from "@/lib/api/subscriptions-client"
import { Repeat, TrendingUp, Calendar, DollarSign, AlertCircle } from "lucide-react"

interface SubscriptionAnalytics {
  activeSubscriptions: number
  totalMRR: number
  totalARR: number
  pendingInvoices: number
  totalRevenue: number
  totalSubscriptions: number
}

export function SubscriptionAnalyticsCard() {
  const [analytics, setAnalytics] = useState<SubscriptionAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const data = await getSubscriptionAnalytics()
        setAnalytics(data)
      } catch (err) {
        setError('Failed to load subscription analytics')
        console.error('Failed to fetch subscription analytics:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace(/IDR\s?/, 'Rp ')
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Repeat className="h-5 w-5" />
            Subscription Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !analytics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Repeat className="h-5 w-5" />
            Subscription Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-muted-foreground">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error || 'No data available'}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Repeat className="h-5 w-5" />
          Subscription Analytics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Active Subscriptions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium">Active Subscriptions</span>
          </div>
          <Badge variant="secondary">
            {analytics.activeSubscriptions} / {analytics.totalSubscriptions}
          </Badge>
        </div>

        {/* Monthly Recurring Revenue */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">Monthly Recurring Revenue</span>
          </div>
          <span className="text-sm font-semibold text-blue-600">
            {formatCurrency(analytics.totalMRR)}
          </span>
        </div>

        {/* Annual Recurring Revenue */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-purple-500" />
            <span className="text-sm font-medium">Annual Recurring Revenue</span>
          </div>
          <span className="text-sm font-semibold text-purple-600">
            {formatCurrency(analytics.totalARR)}
          </span>
        </div>

        {/* Pending Invoices */}
        {analytics.pendingInvoices > 0 && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">Pending Invoices</span>
            </div>
            <Badge variant="outline" className="border-orange-200 text-orange-700">
              {analytics.pendingInvoices}
            </Badge>
          </div>
        )}

        {/* Total Revenue */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium">Total Revenue</span>
          </div>
          <span className="text-sm font-semibold text-green-600">
            {formatCurrency(analytics.totalRevenue)}
          </span>
        </div>

        {/* Quick Actions */}
        {analytics.activeSubscriptions === 0 && (
          <div className="pt-3 border-t">
            <p className="text-xs text-muted-foreground text-center">
              No active subscriptions yet. Create your first subscription project to start tracking recurring revenue.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
