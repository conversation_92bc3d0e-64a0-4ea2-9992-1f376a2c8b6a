'use client'

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSkeleton } from "@/components/ui/loading-skeleton"
import { Pagination } from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { 
  getSubscriptionProjects, 
  searchSubscriptionProjects, 
  pauseSubscription, 
  resumeSubscription, 
  cancelSubscription 
} from "@/lib/api/subscriptions-client"
import { ProjectWithSubscription, SubscriptionStatus, BillingCycle } from "@/lib/types"
import { usePagination } from "@/hooks/usePagination"
import { useDebounce } from "@/hooks/useDebounce"
import { 
  Search, 
  Plus, 
  MoreH<PERSON>zontal, 
  Edit, 
  Pause, 
  Play, 
  X, 
  Eye, 
  Calendar,

  Repeat
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SubscriptionProjectForm } from "./subscription-project-form"

interface SubscriptionListProps {
  onProjectSelect?: (project: ProjectWithSubscription) => void
}

const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'paused', label: 'Paused' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'expired', label: 'Expired' },
]

const billingCycleLabels: Record<BillingCycle, string> = {
  monthly: 'Monthly',
  quarterly: 'Quarterly',
  yearly: 'Yearly',
}

const subscriptionStatusColors: Record<SubscriptionStatus, string> = {
  active: 'bg-green-100 text-green-800',
  paused: 'bg-yellow-100 text-yellow-800',
  cancelled: 'bg-red-100 text-red-800',
  expired: 'bg-gray-100 text-gray-800',
}

export function SubscriptionList({ onProjectSelect }: SubscriptionListProps) {
  const [projects, setProjects] = useState<ProjectWithSubscription[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showForm, setShowForm] = useState(false)
  const [editingProject, setEditingProject] = useState<ProjectWithSubscription | undefined>()

  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Fetch projects
  const fetchProjects = useCallback(async () => {
    setLoading(true)
    try {
      let data: ProjectWithSubscription[]
      
      if (debouncedSearchQuery) {
        data = await searchSubscriptionProjects(debouncedSearchQuery)
      } else {
        data = await getSubscriptionProjects()
      }
      
      setProjects(data)
    } catch (error) {
      console.error('Failed to fetch subscription projects:', error)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  // Memoized filtered projects
  const filteredProjects = useMemo(() => {
    if (!projects.length) return []

    return projects.filter(project => {
      const matchesStatus = statusFilter === 'all' || project.subscription_status === statusFilter
      return matchesStatus
    })
  }, [projects, statusFilter])

  // Pagination hook
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedProjects,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination,
  } = usePagination({ data: filteredProjects, initialItemsPerPage: 20 })

  // Reset pagination when filters change
  useEffect(() => {
    resetPagination()
  }, [debouncedSearchQuery, statusFilter, resetPagination])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace(/IDR\s?/, 'Rp ')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const handleStatusChange = async (project: ProjectWithSubscription, newStatus: SubscriptionStatus) => {
    try {
      switch (newStatus) {
        case 'paused':
          await pauseSubscription(project.id)
          break
        case 'active':
          await resumeSubscription(project.id)
          break
        case 'cancelled':
          await cancelSubscription(project.id)
          break
      }
      fetchProjects()
    } catch (error) {
      console.error('Failed to update subscription status:', error)
    }
  }

  const handleEditProject = (project: ProjectWithSubscription) => {
    setEditingProject(project)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    fetchProjects()
    setEditingProject(undefined)
    setShowForm(false)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingProject(undefined)
  }

  if (loading) {
    return <LoadingSkeleton type="list" count={3} />
  }

  if (showForm) {
    return (
      <SubscriptionProjectForm
        project={editingProject}
        onSuccess={handleFormSuccess}
        onCancel={handleFormClose}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Subscription Projects</h1>
          <p className="text-muted-foreground">
            Manage recurring billing projects and subscriptions
          </p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Subscription
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search subscriptions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Subscription List */}
      {loading ? (
        <LoadingSkeleton type="table" />
      ) : filteredProjects.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Repeat className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No subscription projects found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery || statusFilter !== 'all' 
                ? "Try adjusting your search or filters" 
                : "Get started by creating your first subscription project"}
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <Button onClick={() => setShowForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Subscription Project
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card className="border-border/50 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/40">
                  <TableHead>Project</TableHead>
                  <TableHead className="hidden md:table-cell">Client</TableHead>
                  <TableHead className="hidden sm:table-cell">Billing</TableHead>
                  <TableHead className="hidden lg:table-cell">Next Billing</TableHead>
                  <TableHead className="hidden sm:table-cell">Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedProjects.map((project) => (
                  <TableRow key={project.id} className="border-b border-border/30">
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{project.name}</div>
                        {project.description && (
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {project.description}
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            <Repeat className="mr-1 h-3 w-3" />
                            {billingCycleLabels[project.billing_cycle as BillingCycle]}
                          </Badge>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="space-y-1">
                        <div className="font-medium">{project.client?.name}</div>
                        {project.client?.company && (
                          <div className="text-sm text-muted-foreground">
                            {project.client.company}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="space-y-1">
                        <div className="font-medium">
                          {formatCurrency(project.billing_amount || 0)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          per {project.billing_cycle}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {project.next_billing_date && (
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          {formatDate(project.next_billing_date)}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <Badge 
                        className={subscriptionStatusColors[project.subscription_status as SubscriptionStatus]}
                      >
                        {project.subscription_status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onProjectSelect?.(project)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditProject(project)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          {project.subscription_status === 'active' && (
                            <DropdownMenuItem 
                              onClick={() => handleStatusChange(project, 'paused')}
                            >
                              <Pause className="mr-2 h-4 w-4" />
                              Pause
                            </DropdownMenuItem>
                          )}
                          {project.subscription_status === 'paused' && (
                            <DropdownMenuItem 
                              onClick={() => handleStatusChange(project, 'active')}
                            >
                              <Play className="mr-2 h-4 w-4" />
                              Resume
                            </DropdownMenuItem>
                          )}
                          {project.subscription_status !== 'cancelled' && (
                            <DropdownMenuItem 
                              onClick={() => handleStatusChange(project, 'cancelled')}
                              className="text-destructive"
                            >
                              <X className="mr-2 h-4 w-4" />
                              Cancel
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
