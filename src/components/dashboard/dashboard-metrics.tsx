'use client'

import { memo, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Banknote, FileText, Users, Briefcase } from "lucide-react"
import { DashboardData } from "@/lib/api/dashboard"

interface DashboardMetricsProps {
  data: DashboardData
}

// Optimized DashboardMetrics component with React.memo
export const DashboardMetrics = memo(function DashboardMetrics({ data }: DashboardMetricsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  // Memoize metrics calculation to prevent unnecessary recalculations
  const metrics = useMemo(() => [
    {
      title: "Total Clients",
      value: data.metrics.totalClients.toString(),
      icon: Users,
      description: `${data.metrics.activeClients} active`,
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      title: "Active Projects",
      value: data.metrics.activeProjects.toString(),
      icon: Briefcase,
      description: `${data.metrics.totalProjects} total`,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50 dark:bg-emerald-950"
    },
    {
      title: "Pending Invoices",
      value: data.metrics.pendingInvoices.toString(),
      icon: FileText,
      description: `${data.metrics.totalInvoices} total`,
      color: "text-amber-600",
      bgColor: "bg-amber-50 dark:bg-amber-950"
    },
    {
      title: "Total Revenue",
      value: formatCurrency(data.metrics.totalRevenue),
      icon: Banknote,
      description: "All time",
      color: "text-emerald-600",
      bgColor: "bg-emerald-50 dark:bg-emerald-950"
    },
    {
      title: "Monthly Revenue",
      value: formatCurrency(data.metrics.monthlyRevenue),
      icon: TrendingUp,
      description: "This month",
      color: "text-primary",
      bgColor: "bg-primary/10",
      trend: data.metrics.revenueGrowth
    }
  ], [data.metrics])

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
      {metrics.map((metric) => (
        <Card key={metric.title} className="border-border/50 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {metric.title}
            </CardTitle>
            <div className={`h-10 w-10 rounded-xl ${metric.bgColor} flex items-center justify-center`}>
              <metric.icon className={`h-5 w-5 ${metric.color}`} />
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-2xl font-bold tracking-tight">{metric.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground/80">
                {metric.description}
              </p>
              {metric.trend !== undefined && metric.trend !== 0 && (
                <Badge
                  variant={metric.trend > 0 ? "default" : "destructive"}
                  className="text-xs px-2 py-1 rounded-full"
                >
                  {metric.trend > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {formatPercentage(metric.trend)}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
})
