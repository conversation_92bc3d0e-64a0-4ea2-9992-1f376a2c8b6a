export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users_profiles: {
        Row: {
          id: string
          full_name: string | null
          role: 'admin' | 'manager' | 'employee'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          role?: 'admin' | 'manager' | 'employee'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          role?: 'admin' | 'manager' | 'employee'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          company: string | null
          website: string | null
          address: Json | null
          status: 'lead' | 'active' | 'inactive' | 'archived'
          lead_source: string | null
          assigned_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          company?: string | null
          website?: string | null
          address?: Json | null
          status?: 'lead' | 'active' | 'inactive' | 'archived'
          lead_source?: string | null
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          company?: string | null
          website?: string | null
          address?: Json | null
          status?: 'lead' | 'active' | 'inactive' | 'archived'
          lead_source?: string | null
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          client_id: string
          status: 'planning' | 'in_progress' | 'review' | 'completed' | 'cancelled'
          service_type: 'jasa_pembuatan_website' | 'jasa_redesign_website' | 'wordpress_hosting' | 'jasa_maintenance_website' | 'jasa_perbaikan_website' | 'jasa_remove_malware' | 'jasa_migrasi_hosting' | 'jasa_migrasi_website_ke_astro' | 'jasa_konversi_wordpress_ke_blocks' | 'jasa_audit_optimasi_seo_onpage' | null
          start_date: string | null
          end_date: string | null
          budget: number | null
          assigned_team: Json | null
          created_by: string
          created_at: string
          updated_at: string
          currency: 'USD' | 'IDR' | null
          project_type: 'one_time' | 'subscription' | null
          billing_cycle: 'monthly' | 'quarterly' | 'yearly' | null
          billing_amount: number | null
          billing_start_date: string | null
          billing_end_date: string | null
          next_billing_date: string | null
          subscription_status: 'active' | 'paused' | 'cancelled' | 'expired' | null
          auto_generate_invoices: boolean | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          client_id: string
          status?: 'planning' | 'in_progress' | 'review' | 'completed' | 'cancelled'
          service_type?: 'jasa_pembuatan_website' | 'jasa_redesign_website' | 'wordpress_hosting' | 'jasa_maintenance_website' | 'jasa_perbaikan_website' | 'jasa_remove_malware' | 'jasa_migrasi_hosting' | 'jasa_migrasi_website_ke_astro' | 'jasa_konversi_wordpress_ke_blocks' | 'jasa_audit_optimasi_seo_onpage' | null
          start_date?: string | null
          end_date?: string | null
          budget?: number | null
          assigned_team?: Json | null
          created_by: string
          created_at?: string
          updated_at?: string
          currency?: 'USD' | 'IDR' | null
          project_type?: 'one_time' | 'subscription' | null
          billing_cycle?: 'monthly' | 'quarterly' | 'yearly' | null
          billing_amount?: number | null
          billing_start_date?: string | null
          billing_end_date?: string | null
          next_billing_date?: string | null
          subscription_status?: 'active' | 'paused' | 'cancelled' | 'expired' | null
          auto_generate_invoices?: boolean | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          client_id?: string
          status?: 'planning' | 'in_progress' | 'review' | 'completed' | 'cancelled'
          service_type?: 'jasa_pembuatan_website' | 'jasa_redesign_website' | 'wordpress_hosting' | 'jasa_maintenance_website' | 'jasa_perbaikan_website' | 'jasa_remove_malware' | 'jasa_migrasi_hosting' | 'jasa_migrasi_website_ke_astro' | 'jasa_konversi_wordpress_ke_blocks' | 'jasa_audit_optimasi_seo_onpage' | null
          start_date?: string | null
          end_date?: string | null
          budget?: number | null
          assigned_team?: Json | null
          created_by?: string
          created_at?: string
          updated_at?: string
          currency?: 'USD' | 'IDR' | null
          project_type?: 'one_time' | 'subscription' | null
          billing_cycle?: 'monthly' | 'quarterly' | 'yearly' | null
          billing_amount?: number | null
          billing_start_date?: string | null
          billing_end_date?: string | null
          next_billing_date?: string | null
          subscription_status?: 'active' | 'paused' | 'cancelled' | 'expired' | null
          auto_generate_invoices?: boolean | null
        }
      }
      invoices: {
        Row: {
          id: string
          invoice_number: string
          client_id: string
          project_id: string | null
          amount: number
          tax_amount: number
          total_amount: number
          currency: 'USD' | 'IDR'
          status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
          invoice_date: string | null
          due_date: string | null
          paid_date: string | null
          items: Json | null
          notes: string | null
          created_by: string
          created_at: string
          updated_at: string
          discount_amount: number
          discount_type: 'amount' | 'percentage'
          invoice_type: string
          milestone_type: 'dp' | 'progress' | 'final' | 'standard'
          parent_invoice_id: string | null
          milestone_percentage: number | null
          sequence_number: number
        }
        Insert: {
          id?: string
          invoice_number: string
          client_id: string
          project_id?: string | null
          amount: number
          tax_amount?: number
          total_amount: number
          currency?: 'USD' | 'IDR'
          status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
          invoice_date?: string | null
          due_date?: string | null
          paid_date?: string | null
          items?: Json | null
          notes?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
          discount_amount?: number
          discount_type?: 'amount' | 'percentage'
          invoice_type?: string
          milestone_type?: 'dp' | 'progress' | 'final' | 'standard'
          parent_invoice_id?: string | null
          milestone_percentage?: number | null
          sequence_number?: number
        }
        Update: {
          id?: string
          invoice_number?: string
          client_id?: string
          project_id?: string | null
          amount?: number
          tax_amount?: number
          total_amount?: number
          currency?: 'USD' | 'IDR'
          status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
          invoice_date?: string | null
          due_date?: string | null
          paid_date?: string | null
          items?: Json | null
          notes?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
          discount_amount?: number
          discount_type?: 'amount' | 'percentage'
          invoice_type?: string
          milestone_type?: 'dp' | 'progress' | 'final' | 'standard'
          parent_invoice_id?: string | null
          milestone_percentage?: number | null
          sequence_number?: number
        }
      }
      activities: {
        Row: {
          id: string
          user_id: string
          action: string
          entity_type: string
          entity_id: string | null
          details: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: string
          entity_type: string
          entity_id?: string | null
          details?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: string
          entity_type?: string
          entity_id?: string | null
          details?: Json | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
