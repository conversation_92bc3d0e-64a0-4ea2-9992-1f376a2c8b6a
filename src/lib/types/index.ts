import { Database } from './database'

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Application Types
export type UserProfile = Tables<'users_profiles'>
export type Client = Tables<'clients'>
export type Project = Tables<'projects'>
export type Invoice = Tables<'invoices'>
export type Activity = Tables<'activities'>

// Extended types with relationships
export type ClientWithProjects = Client & {
  projects?: Project[]
}

export type ProjectWithClient = Project & {
  client?: Client
  assigned_users?: UserProfile[]
}

// Extended Project type with joined data from API calls
export type ProjectWithRelations = Project & {
  client?: {
    id: string
    name: string
    company?: string
    email?: string
    phone?: string
  }
  created_by_user?: {
    id: string
    full_name: string
  }
  assigned_team_members?: {
    id: string
    full_name: string
    role?: string
  }[]
}

export type InvoiceWithClient = Invoice & {
  client?: Client
  project?: Project
}

export type InvoiceWithRelations = Invoice & {
  client?: {
    id: string
    name: string
    company?: string
    email?: string
    phone?: string
    address?: Address
  }
  project?: {
    id: string
    name: string
    description?: string
  }
  created_by_user?: {
    id: string
    full_name: string
  }
  milestone_type?: MilestoneType
  milestone_percentage?: number
  parent_invoice_id?: string
  sequence_number?: number
}

// Form types
export type CreateClientData = Inserts<'clients'>
export type UpdateClientData = Updates<'clients'>
export type CreateProjectData = Inserts<'projects'>
export type UpdateProjectData = Updates<'projects'>
export type CreateInvoiceData = Omit<Inserts<'invoices'>, 'invoice_number'>
export type UpdateInvoiceData = Updates<'invoices'>

// Status types
export type ClientStatus = 'lead' | 'active' | 'inactive' | 'archived'
export type ProjectStatus = 'planning' | 'in_progress' | 'review' | 'completed' | 'cancelled'
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
export type MilestoneType = 'dp' | 'progress' | 'final' | 'standard'
export type UserRole = 'admin' | 'manager' | 'employee'

// Subscription types
export type ProjectType = 'one_time' | 'subscription' | null
export type BillingCycle = 'monthly' | 'quarterly' | 'yearly'
export type SubscriptionStatus = 'active' | 'paused' | 'cancelled' | 'expired'
export type SubscriptionInvoiceStatus = 'pending' | 'generated' | 'sent' | 'paid' | 'failed'

// Dashboard types
export interface DashboardMetrics {
  totalClients: number
  activeProjects: number
  pendingInvoices: number
  totalRevenue: number
  monthlyRevenue: number
}

export interface ChartData {
  name: string
  value: number
  date?: string
}

// Invoice item type
export interface InvoiceItem {
  id: string
  description: string
  quantity: number
  rate: number
  amount: number
}

// Address type
export interface Address {
  street?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
}

// Payment milestone types
export interface PaymentMilestone {
  id: string
  type: MilestoneType
  percentage: number
  amount: number
  description: string
  sequence: number
  status: InvoiceStatus
  due_date?: string
  invoice_id?: string
}

export interface ProjectWithMilestones extends ProjectWithRelations {
  milestones?: PaymentMilestone[]
  total_invoiced?: number
  remaining_budget?: number
}

// Subscription invoice type
export interface SubscriptionInvoice {
  id: string
  project_id: string
  invoice_id?: string
  billing_period_start: string
  billing_period_end: string
  amount: number
  currency: string
  status: SubscriptionInvoiceStatus
  due_date: string
  generated_at?: string
  created_at: string
  updated_at: string
}

export interface SubscriptionInvoiceWithRelations extends SubscriptionInvoice {
  project?: ProjectWithRelations
  invoice?: InvoiceWithRelations
}

// Extended project type with subscription data
export interface ProjectWithSubscription extends ProjectWithRelations {
  subscription_invoices?: SubscriptionInvoiceWithRelations[]
}
