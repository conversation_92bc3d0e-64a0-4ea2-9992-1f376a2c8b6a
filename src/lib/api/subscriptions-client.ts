import { createClient as createSup<PERSON><PERSON><PERSON>lient } from '@/lib/supabase/client'
import {
  ProjectWithSubscription,
  SubscriptionInvoiceWithRelations,
  SubscriptionStatus
} from '@/lib/types'

const supabase = createSupabaseClient()

// Get subscription projects
export async function getSubscriptionProjects(): Promise<ProjectWithSubscription[]> {
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name)
    `)
    .eq('project_type', 'subscription')
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch subscription projects: ${error.message}`)
  }

  return data || []
}

// Get subscription project by ID
export async function getSubscriptionProject(id: string): Promise<ProjectWithSubscription | null> {
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name),
      subscription_invoices:subscription_invoices(
        *,
        invoice:invoices(*)
      )
    `)
    .eq('id', id)
    .eq('project_type', 'subscription')
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null
    }
    throw new Error(`Failed to fetch subscription project: ${error.message}`)
  }

  return data
}

// Update subscription project
export async function updateSubscriptionProject(
  id: string, 
  updates: Partial<ProjectWithSubscription>
): Promise<ProjectWithSubscription> {
  const { data, error } = await supabase
    .from('projects')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name)
    `)
    .single()

  if (error) {
    throw new Error(`Failed to update subscription project: ${error.message}`)
  }

  return data
}

// Create subscription project
export async function createSubscriptionProject(projectData: Record<string, unknown>): Promise<ProjectWithSubscription> {
  const { data, error } = await supabase
    .from('projects')
    .insert({
      ...projectData,
      project_type: 'subscription',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name)
    `)
    .single()

  if (error) {
    throw new Error(`Failed to create subscription project: ${error.message}`)
  }

  return data
}

// Get subscription invoices
export async function getSubscriptionInvoices(projectId?: string): Promise<SubscriptionInvoiceWithRelations[]> {
  let query = supabase
    .from('subscription_invoices')
    .select(`
      *,
      project:projects(
        *,
        client:clients(*)
      ),
      invoice:invoices(*)
    `)
    .order('created_at', { ascending: false })

  if (projectId) {
    query = query.eq('project_id', projectId)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch subscription invoices: ${error.message}`)
  }

  return data || []
}

// Generate subscription invoices manually
export async function generateSubscriptionInvoices(): Promise<{ success: boolean; message: string; count: number }> {
  try {
    const { data, error } = await supabase.rpc('generate_subscription_invoices')

    if (error) {
      throw new Error(`Failed to generate subscription invoices: ${error.message}`)
    }

    const count = data?.length || 0
    return {
      success: true,
      message: `Successfully generated ${count} subscription invoices`,
      count
    }
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      count: 0
    }
  }
}

// Update subscription status
export async function updateSubscriptionStatus(
  projectId: string, 
  status: SubscriptionStatus
): Promise<ProjectWithSubscription> {
  const { data, error } = await supabase
    .from('projects')
    .update({
      subscription_status: status,
      updated_at: new Date().toISOString()
    })
    .eq('id', projectId)
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name)
    `)
    .single()

  if (error) {
    throw new Error(`Failed to update subscription status: ${error.message}`)
  }

  return data
}

// Pause subscription
export async function pauseSubscription(projectId: string): Promise<ProjectWithSubscription> {
  return updateSubscriptionStatus(projectId, 'paused')
}

// Resume subscription
export async function resumeSubscription(projectId: string): Promise<ProjectWithSubscription> {
  return updateSubscriptionStatus(projectId, 'active')
}

// Cancel subscription
export async function cancelSubscription(projectId: string): Promise<ProjectWithSubscription> {
  return updateSubscriptionStatus(projectId, 'cancelled')
}

// Get subscription analytics
export async function getSubscriptionAnalytics() {
  const { data: projects, error: projectsError } = await supabase
    .from('projects')
    .select('id, billing_amount, billing_cycle, subscription_status')
    .eq('project_type', 'subscription')

  if (projectsError) {
    throw new Error(`Failed to fetch subscription analytics: ${projectsError.message}`)
  }

  const { data: invoices, error: invoicesError } = await supabase
    .from('subscription_invoices')
    .select('amount, status, created_at')

  if (invoicesError) {
    throw new Error(`Failed to fetch subscription invoice analytics: ${invoicesError.message}`)
  }

  const activeSubscriptions = projects?.filter(p => p.subscription_status === 'active').length || 0
  const totalMRR = projects
    ?.filter(p => p.subscription_status === 'active' && p.billing_cycle === 'monthly')
    .reduce((sum, p) => sum + (p.billing_amount || 0), 0) || 0

  const totalARR = projects
    ?.filter(p => p.subscription_status === 'active')
    .reduce((sum, p) => {
      const amount = p.billing_amount || 0
      switch (p.billing_cycle) {
        case 'monthly': return sum + (amount * 12)
        case 'quarterly': return sum + (amount * 4)
        case 'yearly': return sum + amount
        default: return sum
      }
    }, 0) || 0

  const pendingInvoices = invoices?.filter(i => i.status === 'pending').length || 0
  const totalRevenue = invoices
    ?.filter(i => i.status === 'paid')
    .reduce((sum, i) => sum + i.amount, 0) || 0

  return {
    activeSubscriptions,
    totalMRR,
    totalARR,
    pendingInvoices,
    totalRevenue,
    totalSubscriptions: projects?.length || 0
  }
}

// Search subscription projects
export async function searchSubscriptionProjects(query: string): Promise<ProjectWithSubscription[]> {
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!projects_created_by_fkey(id, full_name)
    `)
    .eq('project_type', 'subscription')
    .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to search subscription projects: ${error.message}`)
  }

  return data || []
}
